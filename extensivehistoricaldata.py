import numpy as np
import matplotlib.pyplot as plt
import ccxt
import pandas as pd
from datetime import datetime, timedelta

class CryptoPriceAnalyzer:
    """
    A class for retrieving and analyzing cryptocurrency historical price data from Binance
    """

    def __init__(self, symbol='SOL/USDT'):
        """
        Initialize the CryptoPriceAnalyzer

        Args:
            symbol (str): Trading pair symbol (e.g., 'SOL/USDT', 'BTC/USDT', 'ETH/USDT')
        """
        self.symbol = symbol
        self.df = None  # DataFrame to store historical data
        self.exchange = ccxt.binance({
            'rateLimit': 500,  # Respect rate limits
            'enableRateLimit': True,
        })

    def fetch_historical_data(self, days=60, timeframe='4h'):
        """
        Retrieve historical prices for the cryptocurrency from Binance

        Args:
            days (int): Number of days of historical data to retrieve
            timeframe (str): Timeframe for the data ('1m', '5m', '15m', '1h', '4h', '1d', '1w')

        Returns:
            pandas.DataFrame: DataFrame with OHLCV data (also stored in self.df)
        """
        try:
            # Calculate the start time (days ago from now)
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)

            # Convert to milliseconds timestamp
            since = int(start_time.timestamp() * 1000)

            print(f"Fetching {self.symbol} historical data from {start_time.strftime('%Y-%m-%d')} to {end_time.strftime('%Y-%m-%d')}")
            print(f"Timeframe: {timeframe}")

            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(self.symbol, timeframe, since)

            # Convert to DataFrame
            self.df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

            # Convert timestamp to datetime
            self.df['datetime'] = pd.to_datetime(self.df['timestamp'], unit='ms')
            self.df.set_index('datetime', inplace=True)

            # Drop the original timestamp column
            self.df.drop('timestamp', axis=1, inplace=True)

            # Calculate moving averages
            self.calculate_moving_averages()

            print(f"Successfully retrieved {len(self.df)} data points")
            return self.df

        except Exception as e:
            print(f"Error fetching data: {e}")
            self.df = None
            return None

    def calculate_moving_averages(self, windows=[5, 20, 50, 100, 200]):
        """
        Calculate moving averages for the closing prices

        Args:
            windows (list): List of window sizes for moving averages
        """
        if self.df is None:
            print("No data available. Please fetch historical data first.")
            return

        for window in windows:
            column_name = f'{window}_day_avg'
            self.df[column_name] = self.df['close'].rolling(window=window, min_periods=1).mean()

    def get_current_price(self):
        """
        Get current cryptocurrency price from Binance

        Returns:
            dict: Current ticker information
        """
        try:
            ticker = self.exchange.fetch_ticker(self.symbol)
            return ticker
        except Exception as e:
            print(f"Error fetching current price: {e}")
            return None

    def plot_prices(self):
        """
        Plot cryptocurrency price data using the stored DataFrame
        """
        if self.df is None or self.df.empty:
            print("No data to plot. Please fetch historical data first.")
            return

        plt.figure(figsize=(12, 8))

        # Plot closing prices and moving averages
        plt.subplot(2, 1, 1)
        plt.plot(self.df.index, self.df['close'], label=f'{self.symbol} Close Price', color='blue', linewidth=2)

        # Plot moving averages if they exist
        if '5_day_avg' in self.df.columns:
            plt.plot(self.df.index, self.df['5_day_avg'], label='5-Day Moving Average', color='red', linewidth=2, linestyle='--')
        if '20_day_avg' in self.df.columns:
            plt.plot(self.df.index, self.df['20_day_avg'], label='20-Day Moving Average', color='green', linewidth=2, linestyle='--')

        plt.title(f'{self.symbol} Historical Prices')
        # Determine currency from symbol
        currency = self.symbol.split('/')[1] if '/' in self.symbol else 'USDT'
        plt.ylabel(f'Price ({currency})')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # Plot volume
        plt.subplot(2, 1, 2)
        plt.bar(self.df.index, self.df['volume'], label='Volume', color='orange', alpha=0.7)
        plt.title('Trading Volume')
        plt.ylabel('Volume')
        plt.xlabel('Date')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def get_statistics(self):
        """
        Calculate and return basic statistics for the stored data

        Returns:
            dict: Dictionary containing price statistics
        """
        if self.df is None or self.df.empty:
            print("No data available. Please fetch historical data first.")
            return None

        stats = {
            'highest': self.df['high'].max(),
            'lowest': self.df['low'].min(),
            'average': self.df['close'].mean(),
            'current': self.df['close'].iloc[-1],
        }

        # Add moving average statistics if they exist
        if '5_day_avg' in self.df.columns:
            stats['current_5_day_avg'] = self.df['5_day_avg'].iloc[-1]
        if '20_day_avg' in self.df.columns:
            stats['current_20_day_avg'] = self.df['20_day_avg'].iloc[-1]

        return stats

    def print_statistics(self):
        """
        Print formatted statistics for the stored data
        """
        stats = self.get_statistics()
        if stats is None:
            return

        print(f"\nPrice Statistics:")
        print(f"   Highest: ${stats['highest']:.4f}")
        print(f"   Lowest: ${stats['lowest']:.4f}")
        print(f"   Average: ${stats['average']:.4f}")
        print(f"   Current: ${stats['current']:.4f}")

        if 'current_5_day_avg' in stats:
            print(f"   Current 5-Day Avg: ${stats['current_5_day_avg']:.4f}")
        if 'current_20_day_avg' in stats:
            print(f"   Current 20-Day Avg: ${stats['current_20_day_avg']:.4f}")

    def save_to_csv(self, filename=None):
        """
        Save the stored DataFrame to a CSV file

        Args:
            filename (str): Optional custom filename. If None, generates automatic name.

        Returns:
            str: The filename used for saving
        """
        if self.df is None or self.df.empty:
            print("No data to save. Please fetch historical data first.")
            return None

        if filename is None:
            pair_name = self.symbol.replace('/', '_')
            filename = f"{pair_name}_historical_prices_{datetime.now().strftime('%Y%m%d')}.csv"

        self.df.to_csv(filename)
        print(f"Data saved to: {filename}")
        return filename

    def run_complete_analysis(self, days=30, timeframe='1d'):
        """
        Run a complete analysis including current price, historical data, statistics, and plotting

        Args:
            days (int): Number of days of historical data to retrieve
            timeframe (str): Timeframe for the data
        """
        print(f"=== {self.symbol} Cryptocurrency Analysis ===\n")

        # Get current price
        print(f"1. Current {self.symbol} Price:")
        current_price = self.get_current_price()
        if current_price:
            currency = self.symbol.split('/')[1]
            print(f"   Current Price: ${current_price['last']:.4f} {currency}")
            print(f"   24h Change: {current_price['percentage']:.2f}%")
            print(f"   24h Volume: {current_price['quoteVolume']:,.0f} {currency}")
        print()

        # Get historical data
        print(f"2. Historical Data (Last {days} days, {timeframe} candles):")
        self.fetch_historical_data(days=days, timeframe=timeframe)

        if self.df is not None and not self.df.empty:
            print("\nFirst 5 rows:")
            print(self.df.head())
            print("\nLast 5 rows:")
            print(self.df.tail())

            # Print statistics
            self.print_statistics()

            # Plot the data
            self.plot_prices()

            # Save to CSV
            filename = self.save_to_csv()

        else:
            print("Failed to retrieve historical data.")

def main(symbol='SOL/USDT'):
    """
    Main function to demonstrate the CryptoPriceAnalyzer class

    Args:
        symbol (str): Trading pair symbol (e.g., 'SOL/USDT', 'BTC/USDT', 'ETH/USDT')
    """
    # Create analyzer instance
    analyzer = CryptoPriceAnalyzer(symbol)

    # Run complete analysis
    analyzer.run_complete_analysis(days=30, timeframe='1d')

    # Example: Get hourly data for last 7 days
    print("\n3. Additional Analysis - Hourly Data (Last 7 days):")
    analyzer.fetch_historical_data(days=7, timeframe='1h')
    if analyzer.df is not None:
        print(f"   Retrieved {len(analyzer.df)} hourly data points")
        analyzer.print_statistics()

if __name__ == "__main__":
    # Example usage with different trading pairs

    # Default: SOL/USDT
    main('SOL/USDT')

    # Uncomment below to try other pairs:
    # main('BTC/USDT')
    # main('ETH/USDT')
    # main('SOL/BTC')

    # Example of using the class directly:
    # analyzer = CryptoPriceAnalyzer('BTC/USDT')
    # analyzer.fetch_historical_data(days=7, timeframe='1h')
    # analyzer.plot_prices()
    # stats = analyzer.get_statistics()
    # print(stats)

    # Note: For SOL/AUD, you might need to use a different exchange
    # as Binance doesn't support this pair directly