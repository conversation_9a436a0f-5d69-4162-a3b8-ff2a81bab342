import ccxt
import datetime
import pandas as pd

# Create Binance exchange instance
exchange = ccxt.binance()

# Define parameters
symbol = 'SOL/USDT'
timeframe = '1h'  # can be '1m', '5m', '1h', '1d', etc.
limit = 500       # max is usually 500-1000 per request depending on exchange
since = exchange.parse8601('2024-07-01T00:00:00Z')  # start date in ISO format

# Fetch OHLCV data
ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, since=since, limit=limit)

# Convert to DataFrame
df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

print(df.head())