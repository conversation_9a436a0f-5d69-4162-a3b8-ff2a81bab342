import numpy as np
import matplotlib.pyplot as plt
import ccxt
import pandas as pd
from datetime import datetime, timedelta

def get_solana_historical_prices(days=30, timeframe='1d'):
    """
    Retrieve historical prices for Solana (SOL) from Binance

    Args:
        days (int): Number of days of historical data to retrieve
        timeframe (str): Timeframe for the data ('1m', '5m', '15m', '1h', '4h', '1d', '1w')

    Returns:
        pandas.DataFrame: DataFrame with OHLCV data
    """

    # Initialize Binance exchange (no API keys needed for public data)
    exchange = ccxt.binance({
        'rateLimit': 1200,  # Respect rate limits
        'enableRateLimit': True,
    })

    try:
        # Calculate the start time (days ago from now)
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        # Convert to milliseconds timestamp
        since = int(start_time.timestamp() * 1000)

        print(f"Fetching SOL/USDT historical data from {start_time.strftime('%Y-%m-%d')} to {end_time.strftime('%Y-%m-%d')}")
        print(f"Timeframe: {timeframe}")

        # Fetch OHLCV data
        ohlcv = exchange.fetch_ohlcv('SOL/USDT', timeframe, since)

        # Convert to DataFrame
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

        # Convert timestamp to datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('datetime', inplace=True)

        # Drop the original timestamp column
        df.drop('timestamp', axis=1, inplace=True)

        print(f"Successfully retrieved {len(df)} data points")
        return df

    except Exception as e:
        print(f"Error fetching data: {e}")
        return None

def plot_solana_prices(df):
    """
    Plot Solana price data

    Args:
        df (pandas.DataFrame): DataFrame with OHLCV data
    """
    if df is None or df.empty:
        print("No data to plot")
        return

    plt.figure(figsize=(12, 8))

    # Plot closing prices
    plt.subplot(2, 1, 1)
    plt.plot(df.index, df['close'], label='SOL/USDT Close Price', color='blue', linewidth=2)
    plt.title('Solana (SOL) Historical Prices')
    plt.ylabel('Price (USDT)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot volume
    plt.subplot(2, 1, 2)
    plt.bar(df.index, df['volume'], label='Volume', color='orange', alpha=0.7)
    plt.title('Trading Volume')
    plt.ylabel('Volume')
    plt.xlabel('Date')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

def get_current_solana_price():
    """
    Get current Solana price from Binance

    Returns:
        dict: Current ticker information
    """
    exchange = ccxt.binance()

    try:
        ticker = exchange.fetch_ticker('SOL/USDT')
        return ticker
    except Exception as e:
        print(f"Error fetching current price: {e}")
        return None

def main():
    """
    Main function to demonstrate the historical price retrieval
    """
    print("=== Solana Historical Price Retrieval ===\n")

    # Get current price
    print("1. Current SOL/USDT Price:")
    current_price = get_current_solana_price()
    if current_price:
        print(f"   Current Price: ${current_price['last']:.4f}")
        print(f"   24h Change: {current_price['percentage']:.2f}%")
        print(f"   24h Volume: {current_price['quoteVolume']:,.0f} USDT")
    print()

    # Get historical data
    print("2. Historical Data (Last 30 days, daily candles):")
    df_daily = get_solana_historical_prices(days=30, timeframe='1d')

    if df_daily is not None:
        print("\nFirst 5 rows:")
        print(df_daily.head())
        print("\nLast 5 rows:")
        print(df_daily.tail())

        # Calculate some basic statistics
        print(f"\nPrice Statistics (Last 30 days):")
        print(f"   Highest: ${df_daily['high'].max():.4f}")
        print(f"   Lowest: ${df_daily['low'].min():.4f}")
        print(f"   Average: ${df_daily['close'].mean():.4f}")
        print(f"   Current: ${df_daily['close'].iloc[-1]:.4f}")

        # Plot the data
        plot_solana_prices(df_daily)

        # Save to CSV
        csv_filename = f"solana_historical_prices_{datetime.now().strftime('%Y%m%d')}.csv"
        df_daily.to_csv(csv_filename)
        print(f"\nData saved to: {csv_filename}")

    # Example: Get hourly data for last 7 days
    print("\n3. Hourly Data (Last 7 days):")
    df_hourly = get_solana_historical_prices(days=7, timeframe='1h')
    if df_hourly is not None:
        print(f"   Retrieved {len(df_hourly)} hourly data points")

if __name__ == "__main__":
    main()