import ccxt 
import pandas as pd 
import numpy as np 
import matplotlib.pyplot as plt 
from datetime import datetime, timedelta 

class CryptoPriceAnalyzer:
    def __init__(self, symbol='SOL/USDT'):

        self._symbol = symbol

        self._df = None  # DataFrame to store historical data

        self._exchange = ccxt.binance({
            'rateLimit': 500,  # Respect rate limits
            'enableRateLimit': True,
        })

    def get_historical_data_4h(self, days=50, timeframe='4h', end_time=None):
        """
        Fetch historical data with customizable end time

        Args:
            days (int): Number of days of historical data to retrieve
            timeframe (str): Timeframe for the data ('1m', '5m', '15m', '1h', '4h', '1d', '1w')
            end_time (datetime or str): End time for data retrieval. If None, uses current time.
                                       Can be datetime object or string in format 'YYYY-MM-DD HH:MM:SS'
        """
        try:
            # Handle end_time parameter
            if end_time is None:
                end_time = datetime.now()
            elif isinstance(end_time, str):
                # Parse string to datetime
                try:
                    end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    try:
                        # Try alternative format
                        end_time = datetime.strptime(end_time, '%Y-%m-%d')
                    except ValueError:
                        print(f"Invalid date format: {end_time}. Using current time instead.")
                        end_time = datetime.now()

            start_time = end_time - timedelta(days=days)

            since = int(start_time.timestamp() * 1000)
            print(f"Fetching {self._symbol} historical data from {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Timeframe: {timeframe}")

            ohlcv = self._exchange.fetch_ohlcv(self._symbol, timeframe=timeframe, since=since)

            if self._df is None:
                self._df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            else:
                new_df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                self._df = pd.concat([self._df, new_df], ignore_index=True)
                # Remove duplicates based on timestamp
                self._df = self._df.drop_duplicates(subset=['timestamp'], keep='last')

            return self._df

        except Exception as e:
            print(f"Error fetching data: {e}")
            return None
        
    def fix_time_format(self):
        self._df['datetime'] = pd.to_datetime(self._df['timestamp'], unit='ms')
        self._df.set_index('datetime', inplace=True)


    def calculate_moving_averages(self, windows=[5, 20]):
        """
        Calculate moving averages for the closing prices

        Args:
            windows (list): List of window sizes for moving averages
        """
        if self._df is None:
            print("No data available. Please fetch historical data first.")
            return

        for window in windows:
            column_name = f'{window}_day_avg'
            self._df[column_name] = self._df['close'].rolling(window=window, min_periods=1).mean()
    

# Example usage:
cpa = CryptoPriceAnalyzer()

# Option 1: Use current time (default)
cpa.get_historical_data_4h(days=50, timeframe='4h')

# Option 2: Use specific end time as string
cpa.get_historical_data_4h(days=50, timeframe='4h', end_time=datetime.now()-timedelta(days=50))

# Option 3: Use datetime object
# end_datetime = datetime(2025, 7, 29, 19, 37, 52)
# cpa.get_historical_data_4h(days=50, timeframe='4h', end_time=end_datetime)

cpa.fix_time_format()
cpa.calculate_moving_averages([5, 20])
print(cpa._df)
 

