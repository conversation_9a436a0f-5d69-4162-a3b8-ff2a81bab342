import ccxt 
import pandas as pd 
import numpy as np 
import matplotlib.pyplot as plt 
from datetime import datetime, timedelta 

class CryptoPriceAnalyzer:
    def __init__(self, symbol='SOL/USDT'):

        self._symbol = symbol

        self._df = None  # DataFrame to store historical data

        self._exchange = ccxt.binance({
            'rateLimit': 500,  # Respect rate limits
            'enableRateLimit': True,
        })

    def get_historical_data_4h(self, days=None, timeframe='4h', start_time=None, end_time=None):
        """
        Fetch historical data with customizable start and end times

        Args:
            days (int): Number of days of historical data to retrieve (used if start_time is None)
            timeframe (str): Timeframe for the data ('1m', '5m', '15m', '1h', '4h', '1d', '1w')
            start_time (datetime or str): Start time for data retrieval. Can be datetime object or string in format 'YYYY-MM-DD HH:MM:SS'
            end_time (datetime or str): End time for data retrieval. If None, uses current time.
                                       Can be datetime object or string in format 'YYYY-MM-DD HH:MM:SS'
        """
        try:
            # Helper function to parse datetime
            def parse_datetime(dt_input, default_value):
                if dt_input is None:
                    return default_value
                elif isinstance(dt_input, str):
                    try:
                        return datetime.strptime(dt_input, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            return datetime.strptime(dt_input, '%Y-%m-%d')
                        except ValueError:
                            print(f"Invalid date format: {dt_input}. Using default value instead.")
                            return default_value
                elif isinstance(dt_input, datetime):
                    return dt_input
                else:
                    print(f"Invalid datetime type: {type(dt_input)}. Using default value instead.")
                    return default_value

            # Handle end_time parameter
            if end_time is None:
                end_time = datetime.now()
            else:
                end_time = parse_datetime(end_time, datetime.now())

            # Handle start_time parameter
            if start_time is None:
                if days is None:
                    days = 50  # Default value
                start_time = end_time - timedelta(days=days)
            else:
                start_time = parse_datetime(start_time, end_time - timedelta(days=50))

            since = int(start_time.timestamp() * 1000)
            print(f"Fetching {self._symbol} historical data from {start_time.strftime('%Y-%m-%d %H:%M:%S')} to {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Timeframe: {timeframe}")

            ohlcv = self._exchange.fetch_ohlcv(self._symbol, timeframe=timeframe, since=since)

            if self._df is None:
                self._df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            else:
                new_df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                self._df = pd.concat([self._df, new_df], ignore_index=True)
                # Remove duplicates based on timestamp
                self._df = self._df.drop_duplicates(subset=['timestamp'], keep='last')

            return self._df

        except Exception as e:
            print(f"Error fetching data: {e}")
            return None
        
    def fix_time_format(self):
        #self._df['datetime'] = pd.to_datetime(self._df['timestamp'], unit='ms')
        self._df['datetime'] = pd.to_datetime(self._df['timestamp'], unit='ms').dt.strftime('%Y-%m-%d %H:%M:%S')
        self._df.set_index('datetime', inplace=True)


    def calculate_moving_averages(self, windows=[5, 20]):
        """
        Calculate moving averages for the closing prices

        Args:
            windows (list): List of window sizes for moving averages
        """
        if self._df is None:
            print("No data available. Please fetch historical data first.")
            return

        for window in windows:
            column_name = f'{window}_day_avg'
            self._df[column_name] = self._df['close'].rolling(window=window, min_periods=1).mean()
    def write_to_csv(self, filename=None):
        """
        Save the stored DataFrame to a CSV file

        Args:
            filename (str): Optional custom filename. If None, uses 'historicaldata.csv'.

        Returns:
            str: The filename used for saving
        """
        if self._df is None or self._df.empty:
            print("No data to save. Please fetch historical data first.")
            return None

        if filename is None:
            filename = "historicaldata.csv"

        self._df.to_csv(filename)
        print(f"Data saved to: {filename}")
        return filename
    

# Example usage:
cpa = CryptoPriceAnalyzer()

# Option 1: Use days parameter (calculates start_time automatically)
# cpa.get_historical_data_4h(days=50, timeframe='4h')

# Option 2: Use specific end time with days
# cpa.get_historical_data_4h(days=50, timeframe='4h', end_time='2024-01-29 19:37:52')

# Option 3: Use both start_time and end_time (days parameter ignored)
# cpa.get_historical_data_4h(timeframe='4h', start_time='2024-01-01 00:00:00', end_time='2024-01-29 19:37:52')

# Option 4: Use datetime objects
# start_dt = datetime(2024, 1, 1, 0, 0, 0)
# end_dt = datetime(2024, 1, 29, 19, 37, 52)
# cpa.get_historical_data_4h(timeframe='4h', start_time=start_dt, end_time=end_dt)

# Current example: Use specific date range
cpa.get_historical_data_4h(timeframe='4h', start_time='2025-06-29 00:00:00', end_time='2025-07-29 00:00:00')
cpa.get_historical_data_4h(timeframe='4h', start_time='2025-05-29 00:00:00', end_time='2025-06-29 00:00:00')
# cpa.get_historical_data_4h(timeframe='4h', start_time='2025-02-01 00:00:00', end_time='2025-04-01 00:00:00')
cpa.fix_time_format()
cpa.calculate_moving_averages([5, 20])
print(cpa._df)
cpa.write_to_csv(filename='historicaldata.csv')
 

