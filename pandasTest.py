import ccxt 
import pandas as pd 
import numpy as np 
import matplotlib.pyplot as plt 
from datetime import datetime, timedelta 

class CryptoPriceAnalyzer:
    def __init__(self, symbol='SOL/USDT'):

        self._symbol = symbol

        self._df = None  # DataFrame to store historical data

        self._exchange = ccxt.binance({
            'rateLimit': 500,  # Respect rate limits
            'enableRateLimit': True,
        })

    def get_historical_data_4h(self , days=50, timeframe='4h', end = datetime.now()):
        print(startDate)

        try:
            end_time = startDate
            start_time = end_time - timedelta(days=days)

            since = int(start_time.timestamp() * 1000)
            print(f"fetching {self._symbol} historical data from {start_time.strftime('%Y-%m-%d')} to {end_time.strftime('%Y-%m-%d')}")
            print(f"Timeframe: {timeframe}")

            ohlcv = self._exchange.fetch_ohlcv(self._symbol, timeframe = timeframe, since=since)
            if self._df == None:
                self._df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            else:
                self._df = pd.concat(pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']))


            return self._df


        except Exception as e:
            print(f"Error fetching data: {e}")
            return None 
        
    def fix_time_format(self):
        self._df['datetime'] = pd.to_datetime(self._df['timestamp'], unit='ms')
        self._df.set_index('datetime', inplace=True)


    def calculate_moving_averages(self, windows=[5, 20]):
        """
        Calculate moving averages for the closing prices

        Args:
            windows (list): List of window sizes for moving averages
        """
        if self._df is None:
            print("No data available. Please fetch historical data first.")
            return

        for window in windows:
            column_name = f'{window}_day_avg'
            self._df[column_name] = self._df['close'].rolling(window=window, min_periods=1).mean()
    

# cpa = CryptoPriceAnalyzer()
# cpa.get_historical_data_4h(50,'4h','2025-07-29 19:37:52.271108')
# cpa.fix_time_format()
# cpa.calculate_moving_averages([5, 20])
# print(cpa._df)  
 

print(type(datetime.now())