import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.dates import date2num

# Read the CSV file
try:
    df = pd.read_csv('historicaldata.csv')
    print(f"Successfully loaded data with {len(df)} rows")
except FileNotFoundError:
    print("Error: historicaldata.csv file not found!")
    exit()

# Extract datetime and 5-day average data
try:
    rec5ave = df[['datetime', '50_day_avg']].to_numpy()
    dates = pd.to_datetime(rec5ave[:,0])
    rec5ave_values = rec5ave[:,1].astype('float')
    print(rec5ave_values)
    # Remove any NaN values
    valid_mask = ~np.isnan(rec5ave_values)
    dates = dates[valid_mask]
    rec5ave_values = rec5ave_values[valid_mask]

    print(f"Processing {len(dates)} valid data points")

except KeyError as e:
    print(f"Error: Required column not found in CSV: {e}")
    exit()
except Exception as e:
    print(f"Error processing data: {e}")
    exit()

plt.figure(figsize=(10, 6))
plt.plot(dates,rec5ave_values, marker='o', linestyle='-', color='red')
plt.xlabel('dates')
plt.ylabel('5 record average')

plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()


# Convert dates to numeric values for polynomial fitting
x_numeric = date2num(dates)
y = np.array(rec5ave_values)

# Fit polynomial (degree 4)
p4 = np.poly1d(np.polyfit(x_numeric, y, 10))

# Create smooth line for plotting
xp_numeric = np.linspace(x_numeric[0], x_numeric[-1], num=100)
xp_dates = pd.to_datetime(xp_numeric, origin='1970-01-01', unit='D')

plt.plot(xp_dates, p4(xp_numeric), c="blue", label='4th degree polynomial fit')
plt.legend()
plt.show()

 # values of the column '5_day_avg' in the dataframe df ### #### = numpy_array
print(rec5ave)